# 硬字幕提取功能集成说明

## 功能位置

硬字幕提取功能已成功集成到分集简介菜单下，作为一个独立的子菜单项。

### 访问路径
1. 打开应用主界面
2. 在左侧导航栏中找到"分集简介"菜单
3. 展开菜单后可以看到两个子选项：
   - **AI生成** - 原有的分集简介生成功能
   - **硬字幕提取** - 新增的硬字幕提取功能

## 功能特性

### 🎯 核心功能
- **视频上传**: 支持拖拽或点击上传视频文件
- **智能配置**: 提供丰富的提取参数配置
- **实时进度**: 显示提取进度和状态
- **结果预览**: 预览提取的字幕内容
- **多格式导出**: 支持SRT、VTT、ASS、TXT格式

### 📊 界面布局
- **标签页设计**: 分为4个标签页，操作流程清晰
- **响应式布局**: 适配不同屏幕尺寸
- **状态指示**: 实时显示处理状态和进度
- **统计信息**: 显示提取结果的详细统计

## 技术实现

### 文件结构
```
components/
├── hardcode-subtitle-extractor.tsx    # 主要组件
├── sidebar-navigation.tsx             # 导航菜单配置
└── sidebar-layout.tsx                 # 路由处理

lib/
├── video-processor.ts                 # 视频处理工具
├── ocr-recognition.ts                 # OCR识别模块
└── subtitle-processor.ts              # 字幕后处理

pages/api/
└── ocr.ts                            # OCR API端点

models/                               # PP-OCRv5模型文件
├── det.onnx                         # 检测模型
├── rec.onnx                         # 识别模型
└── dict.txt                         # 字典文件
```

### 路由配置
- 菜单ID: `content-generation`
- 子菜单ID: `hardcode-subtitle-extractor`
- 完整路由: `content-generation-hardcode-subtitle-extractor`

## 使用前准备

### 1. 模型文件配置
```bash
# 复制模型文件到项目目录
copy "D:\.background\modle\det.onnx" "models\det.onnx"
copy "D:\.background\modle\rec.onnx" "models\rec.onnx"
copy "D:\.background\modle\dict.txt" "models\dict.txt"
```

### 2. 依赖安装
确保已安装必要的依赖：
```bash
npm install onnxruntime-node sharp formidable
npm install --save-dev @types/formidable
```

### 3. 测试模型配置
```bash
npm run test:ocr
```

## 使用流程

### 步骤1: 访问功能
1. 启动应用: `npm run dev`
2. 在左侧导航栏点击"分集简介"
3. 选择"硬字幕提取"子菜单

### 步骤2: 上传视频
- 支持的格式: MP4, AVI, MOV, MKV等
- 可以拖拽文件或点击选择
- 自动获取视频信息

### 步骤3: 配置参数
- **视频处理**: 设置时间范围和帧间隔
- **OCR识别**: 配置置信度和字幕区域
- **字幕处理**: 设置过滤和合并规则
- **输出格式**: 选择导出格式和编码

### 步骤4: 开始提取
- 点击"开始提取"按钮
- 查看实时进度
- 等待处理完成

### 步骤5: 预览和导出
- 在结果预览页查看提取的字幕
- 检查字幕质量和准确性
- 点击"导出字幕"下载文件

## 配置建议

### 字幕区域设置
根据视频类型调整字幕区域：
- **电影/电视剧**: X:10%, Y:75%, 宽度:80%, 高度:20%
- **动漫**: X:15%, Y:80%, 宽度:70%, 高度:15%
- **综艺节目**: X:5%, Y:70%, 宽度:90%, 高度:25%

### 性能优化
- **快速预览**: 帧间隔2-3秒，置信度0.6-0.7
- **标准质量**: 帧间隔1-1.5秒，置信度0.7-0.8
- **高质量**: 帧间隔0.5-1秒，置信度0.8-0.9

## 故障排除

### 常见问题

1. **菜单项不显示**
   - 检查sidebar-navigation.tsx中的菜单配置
   - 确认FileVideo图标已正确导入

2. **页面无法加载**
   - 检查sidebar-layout.tsx中的路由配置
   - 确认HardcodeSubtitleExtractor组件已正确导入

3. **模型加载失败**
   - 检查models目录下的文件是否完整
   - 运行测试脚本验证配置

4. **OCR识别效果差**
   - 调整字幕区域设置
   - 降低置信度阈值
   - 检查视频质量

## 技术细节

### 组件架构
- **独立页面**: 不再是对话框，而是完整的页面组件
- **状态管理**: 使用React Hooks管理复杂状态
- **错误处理**: 完善的错误处理和用户反馈
- **性能优化**: 客户端处理减少服务器负载

### API设计
- **RESTful接口**: 标准的HTTP API设计
- **流式处理**: 支持大文件的分块处理
- **错误恢复**: 自动重试和错误恢复机制

### 安全考虑
- **文件验证**: 严格的文件类型和大小检查
- **临时文件**: 自动清理临时文件
- **内存管理**: 优化内存使用避免泄漏

## 更新日志

### v1.0.0 (当前版本)
- ✅ 集成到分集简介子菜单
- ✅ 完整的用户界面设计
- ✅ PP-OCRv5 ONNX模型支持
- ✅ 多格式字幕导出
- ✅ 智能字幕后处理
- ✅ 详细的使用文档

### 后续计划
- 🔄 支持批量视频处理
- 🔄 添加字幕编辑功能
- 🔄 支持更多语言模型
- 🔄 云端模型服务集成
