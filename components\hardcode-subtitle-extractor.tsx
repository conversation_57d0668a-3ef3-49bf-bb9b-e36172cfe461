"use client"

import React, { useState, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"
import { VideoProcessor } from "@/lib/video-processor"
import { SubtitleProcessor } from "@/lib/subtitle-processor"
import {
  Upload,
  Play,
  Pause,
  Download,
  Settings,
  FileVideo,
  FileText,
  Loader2,
  CheckCircle2,
  AlertCircle,
  Eye,
  EyeOff,
  RotateCcw,
  Trash2,
  Copy,
  Save
} from "lucide-react"

// 字幕提取配置接口
interface ExtractionConfig {
  // 视频处理配置
  startTime: number // 开始时间（秒）
  endTime: number // 结束时间（秒）
  frameInterval: number // 帧间隔（秒）
  
  // OCR配置
  ocrConfidence: number // OCR置信度阈值
  subtitleRegion: {
    x: number
    y: number
    width: number
    height: number
  } // 字幕区域（百分比）
  
  // 字幕处理配置
  minDuration: number // 最小字幕持续时间（秒）
  maxDuration: number // 最大字幕持续时间（秒）
  mergeSimilar: boolean // 合并相似字幕
  removeEmpty: boolean // 移除空字幕
  
  // 输出配置
  outputFormat: 'srt' | 'vtt' | 'ass' | 'txt'
  encoding: 'utf-8' | 'gbk'
}

// 字幕条目接口
interface SubtitleItem {
  id: string
  startTime: number
  endTime: number
  text: string
  confidence: number
  frameIndex: number
}

// 视频信息接口
interface VideoInfo {
  name: string
  duration: number
  fps: number
  width: number
  height: number
  size: number
}

// 提取状态
type ExtractionStatus = 'idle' | 'processing' | 'completed' | 'error'

export function HardcodeSubtitleExtractor() {
  const { toast } = useToast()
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 状态管理
  const [videoFile, setVideoFile] = useState<File | null>(null)
  const [videoInfo, setVideoInfo] = useState<VideoInfo | null>(null)
  const [videoUrl, setVideoUrl] = useState<string>("")
  const [extractionStatus, setExtractionStatus] = useState<ExtractionStatus>('idle')
  const [extractionProgress, setExtractionProgress] = useState(0)
  const [subtitles, setSubtitles] = useState<SubtitleItem[]>([])
  const [selectedSubtitle, setSelectedSubtitle] = useState<SubtitleItem | null>(null)
  const [previewFrame, setPreviewFrame] = useState<string>("")

  // 配置状态
  const [config, setConfig] = useState<ExtractionConfig>({
    startTime: 0,
    endTime: 0,
    frameInterval: 1,
    ocrConfidence: 0.8,
    subtitleRegion: {
      x: 10,
      y: 70,
      width: 80,
      height: 25
    },
    minDuration: 0.5,
    maxDuration: 10,
    mergeSimilar: true,
    removeEmpty: true,
    outputFormat: 'srt',
    encoding: 'utf-8'
  })

  // 处理视频文件上传
  const handleVideoUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // 检查文件类型
    if (!file.type.startsWith('video/')) {
      toast({
        title: "文件格式错误",
        description: "请选择视频文件",
        variant: "destructive"
      })
      return
    }

    setVideoFile(file)
    const url = URL.createObjectURL(file)
    setVideoUrl(url)

    // 获取视频信息
    const video = document.createElement('video')
    video.src = url
    
    video.onloadedmetadata = () => {
      const info: VideoInfo = {
        name: file.name,
        duration: video.duration,
        fps: 30, // 默认帧率，实际应该从视频元数据获取
        width: video.videoWidth,
        height: video.videoHeight,
        size: file.size
      }
      
      setVideoInfo(info)
      setConfig(prev => ({
        ...prev,
        endTime: video.duration
      }))
      
      toast({
        title: "视频加载成功",
        description: `时长: ${Math.round(video.duration)}秒, 分辨率: ${video.videoWidth}x${video.videoHeight}`
      })
    }
  }, [toast])

  // 开始字幕提取
  const handleStartExtraction = useCallback(async () => {
    if (!videoFile || !videoInfo) {
      toast({
        title: "请先选择视频文件",
        variant: "destructive"
      })
      return
    }

    setExtractionStatus('processing')
    setExtractionProgress(0)
    setSubtitles([])

    try {
      // 使用客户端视频处理提取帧
      const frames = await VideoProcessor.extractFrames(videoFile, {
        startTime: config.startTime,
        endTime: config.endTime,
        frameInterval: config.frameInterval,
        subtitleRegion: config.subtitleRegion
      })

      const extractedSubtitles: SubtitleItem[] = []

      // 处理每一帧
      for (let i = 0; i < frames.length; i++) {
        const frame = frames[i]
        setExtractionProgress((i / frames.length) * 100)

        // 预处理图像
        const processedCanvas = VideoProcessor.preprocessImageForOCR(frame.canvas)
        const base64Image = VideoProcessor.canvasToBase64(processedCanvas)

        // 调用OCR API
        try {
          const response = await fetch('/api/ocr', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              imageData: base64Image
            })
          })

          if (response.ok) {
            const ocrResult = await response.json()

            if (ocrResult.success && ocrResult.text && ocrResult.confidence >= config.ocrConfidence) {
              // 检查是否与前一条字幕相似
              const lastSubtitle = extractedSubtitles[extractedSubtitles.length - 1]
              const shouldMerge = config.mergeSimilar &&
                                lastSubtitle &&
                                VideoProcessor.calculateTextSimilarity(lastSubtitle.text, ocrResult.text) > 0.8

              if (shouldMerge) {
                // 合并到前一条字幕
                lastSubtitle.endTime = frame.timestamp + config.frameInterval
              } else {
                // 创建新的字幕条目
                const duration = config.frameInterval
                if (duration >= config.minDuration && duration <= config.maxDuration) {
                  extractedSubtitles.push({
                    id: `subtitle_${i}`,
                    startTime: frame.timestamp,
                    endTime: frame.timestamp + duration,
                    text: ocrResult.text.trim(),
                    confidence: ocrResult.confidence,
                    frameIndex: i
                  })
                }
              }
            }
          }
        } catch (ocrError) {
          console.warn(`帧 ${i} OCR识别失败:`, ocrError)
        }
      }

      // 字幕后处理
      const finalSubtitles = SubtitleProcessor.processSubtitles(extractedSubtitles, {
        minDuration: config.minDuration,
        maxDuration: config.maxDuration,
        mergeSimilar: config.mergeSimilar,
        removeEmpty: config.removeEmpty,
        similarityThreshold: 0.8,
        timeGapThreshold: 2.0
      })

      setSubtitles(finalSubtitles)
      setExtractionStatus('completed')
      setExtractionProgress(100)

      toast({
        title: "字幕提取完成",
        description: `成功提取 ${finalSubtitles.length} 条字幕`
      })

    } catch (error) {
      console.error('字幕提取失败:', error)
      setExtractionStatus('error')
      toast({
        title: "提取失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive"
      })
    }
  }, [videoFile, videoInfo, config, toast])

  // 导出字幕
  const handleExportSubtitles = useCallback(() => {
    if (subtitles.length === 0) {
      toast({
        title: "没有字幕可导出",
        variant: "destructive"
      })
      return
    }

    let content = ""
    const filename = `${videoInfo?.name || 'subtitles'}.${config.outputFormat}`

    switch (config.outputFormat) {
      case 'srt':
        content = subtitles.map((sub, index) => {
          const startTime = formatTime(sub.startTime)
          const endTime = formatTime(sub.endTime)
          return `${index + 1}\n${startTime} --> ${endTime}\n${sub.text}\n`
        }).join('\n')
        break
      
      case 'vtt':
        content = 'WEBVTT\n\n' + subtitles.map(sub => {
          const startTime = formatTimeVTT(sub.startTime)
          const endTime = formatTimeVTT(sub.endTime)
          return `${startTime} --> ${endTime}\n${sub.text}\n`
        }).join('\n')
        break
      
      case 'txt':
        content = subtitles.map(sub => sub.text).join('\n')
        break
    }

    // 下载文件
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)

    toast({
      title: "导出成功",
      description: `字幕已保存为 ${filename}`
    })
  }, [subtitles, config.outputFormat, videoInfo, toast])

  // 格式化时间（使用VideoProcessor中的方法）
  const formatTime = VideoProcessor.formatTimeForSRT
  const formatTimeVTT = VideoProcessor.formatTimeForVTT

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-orange-50/30 to-red-50/30 dark:from-orange-950/20 dark:to-red-950/20">
      {/* 头部信息 */}
      <div className="flex-shrink-0 p-6 border-b border-orange-100/50 dark:border-orange-900/30 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="absolute inset-0 bg-orange-500 blur-md opacity-20 rounded-full"></div>
              <div className="relative bg-gradient-to-br from-orange-500 to-red-600 p-2 rounded-full text-white">
                <FileVideo className="h-5 w-5" />
              </div>
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
                硬字幕提取
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                基于PP-OCRv5模型，智能提取视频中的硬编码字幕
              </p>
            </div>
          </div>
          <Badge variant={extractionStatus === 'completed' ? 'default' : 'secondary'}>
            {extractionStatus === 'idle' && '待处理'}
            {extractionStatus === 'processing' && '处理中'}
            {extractionStatus === 'completed' && '已完成'}
            {extractionStatus === 'error' && '错误'}
          </Badge>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-hidden p-6">

      <Tabs defaultValue="upload" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="upload">视频上传</TabsTrigger>
          <TabsTrigger value="config">提取配置</TabsTrigger>
          <TabsTrigger value="process">处理进度</TabsTrigger>
          <TabsTrigger value="result">结果预览</TabsTrigger>
        </TabsList>

        {/* 视频上传标签页 */}
        <TabsContent value="upload" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileVideo className="h-5 w-5" />
                选择视频文件
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="video/*"
                  onChange={handleVideoUpload}
                  className="hidden"
                />
                <FileVideo className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-lg font-medium mb-2">选择或拖拽视频文件</p>
                <p className="text-sm text-gray-500 mb-4">支持 MP4, AVI, MOV, MKV 等格式</p>
                <Button onClick={() => fileInputRef.current?.click()}>
                  <Upload className="h-4 w-4 mr-2" />
                  选择文件
                </Button>
              </div>

              {videoInfo && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium mb-2">视频信息</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>文件名: {videoInfo.name}</div>
                    <div>时长: {Math.round(videoInfo.duration)}秒</div>
                    <div>分辨率: {videoInfo.width}x{videoInfo.height}</div>
                    <div>大小: {(videoInfo.size / 1024 / 1024).toFixed(2)}MB</div>
                  </div>
                </div>
              )}

              {videoUrl && (
                <div className="mt-4">
                  <video
                    ref={videoRef}
                    src={videoUrl}
                    controls
                    className="w-full max-h-64 rounded-lg"
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 提取配置标签页 */}
        <TabsContent value="config" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 视频处理配置 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  视频处理配置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>开始时间（秒）</Label>
                  <Input
                    type="number"
                    value={config.startTime}
                    onChange={(e) => setConfig(prev => ({ ...prev, startTime: Number(e.target.value) }))}
                    min={0}
                    max={videoInfo?.duration || 0}
                  />
                </div>
                <div className="space-y-2">
                  <Label>结束时间（秒）</Label>
                  <Input
                    type="number"
                    value={config.endTime}
                    onChange={(e) => setConfig(prev => ({ ...prev, endTime: Number(e.target.value) }))}
                    min={0}
                    max={videoInfo?.duration || 0}
                  />
                </div>
                <div className="space-y-2">
                  <Label>帧间隔（秒）</Label>
                  <Slider
                    value={[config.frameInterval]}
                    onValueChange={([value]) => setConfig(prev => ({ ...prev, frameInterval: value }))}
                    min={0.1}
                    max={5}
                    step={0.1}
                    className="w-full"
                  />
                  <div className="text-sm text-gray-500">{config.frameInterval}秒</div>
                </div>
              </CardContent>
            </Card>

            {/* OCR配置 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  OCR识别配置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>置信度阈值</Label>
                  <Slider
                    value={[config.ocrConfidence]}
                    onValueChange={([value]) => setConfig(prev => ({ ...prev, ocrConfidence: value }))}
                    min={0.1}
                    max={1}
                    step={0.1}
                    className="w-full"
                  />
                  <div className="text-sm text-gray-500">{(config.ocrConfidence * 100).toFixed(0)}%</div>
                </div>

                <div className="space-y-2">
                  <Label>字幕区域设置</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label className="text-xs">X位置(%)</Label>
                      <Input
                        type="number"
                        value={config.subtitleRegion.x}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          subtitleRegion: { ...prev.subtitleRegion, x: Number(e.target.value) }
                        }))}
                        min={0}
                        max={100}
                      />
                    </div>
                    <div>
                      <Label className="text-xs">Y位置(%)</Label>
                      <Input
                        type="number"
                        value={config.subtitleRegion.y}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          subtitleRegion: { ...prev.subtitleRegion, y: Number(e.target.value) }
                        }))}
                        min={0}
                        max={100}
                      />
                    </div>
                    <div>
                      <Label className="text-xs">宽度(%)</Label>
                      <Input
                        type="number"
                        value={config.subtitleRegion.width}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          subtitleRegion: { ...prev.subtitleRegion, width: Number(e.target.value) }
                        }))}
                        min={1}
                        max={100}
                      />
                    </div>
                    <div>
                      <Label className="text-xs">高度(%)</Label>
                      <Input
                        type="number"
                        value={config.subtitleRegion.height}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          subtitleRegion: { ...prev.subtitleRegion, height: Number(e.target.value) }
                        }))}
                        min={1}
                        max={100}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 字幕处理配置 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  字幕处理配置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>最小持续时间（秒）</Label>
                  <Input
                    type="number"
                    value={config.minDuration}
                    onChange={(e) => setConfig(prev => ({ ...prev, minDuration: Number(e.target.value) }))}
                    min={0.1}
                    max={10}
                    step={0.1}
                  />
                </div>
                <div className="space-y-2">
                  <Label>最大持续时间（秒）</Label>
                  <Input
                    type="number"
                    value={config.maxDuration}
                    onChange={(e) => setConfig(prev => ({ ...prev, maxDuration: Number(e.target.value) }))}
                    min={1}
                    max={60}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="mergeSimilar"
                    checked={config.mergeSimilar}
                    onCheckedChange={(checked) => setConfig(prev => ({ ...prev, mergeSimilar: !!checked }))}
                  />
                  <Label htmlFor="mergeSimilar">合并相似字幕</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="removeEmpty"
                    checked={config.removeEmpty}
                    onCheckedChange={(checked) => setConfig(prev => ({ ...prev, removeEmpty: !!checked }))}
                  />
                  <Label htmlFor="removeEmpty">移除空字幕</Label>
                </div>
              </CardContent>
            </Card>

            {/* 输出配置 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="h-5 w-5" />
                  输出配置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>输出格式</Label>
                  <Select
                    value={config.outputFormat}
                    onValueChange={(value: 'srt' | 'vtt' | 'ass' | 'txt') =>
                      setConfig(prev => ({ ...prev, outputFormat: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="srt">SRT (SubRip)</SelectItem>
                      <SelectItem value="vtt">VTT (WebVTT)</SelectItem>
                      <SelectItem value="ass">ASS (Advanced SSA)</SelectItem>
                      <SelectItem value="txt">TXT (纯文本)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>文件编码</Label>
                  <Select
                    value={config.encoding}
                    onValueChange={(value: 'utf-8' | 'gbk') =>
                      setConfig(prev => ({ ...prev, encoding: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="utf-8">UTF-8</SelectItem>
                      <SelectItem value="gbk">GBK</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 处理进度标签页 */}
        <TabsContent value="process" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Loader2 className={`h-5 w-5 ${extractionStatus === 'processing' ? 'animate-spin' : ''}`} />
                提取进度
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>整体进度</span>
                  <span>{extractionProgress.toFixed(1)}%</span>
                </div>
                <Progress value={extractionProgress} className="w-full" />
              </div>

              <div className="flex gap-4">
                <Button
                  onClick={handleStartExtraction}
                  disabled={!videoFile || extractionStatus === 'processing'}
                  className="flex-1"
                >
                  {extractionStatus === 'processing' ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      处理中...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      开始提取
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  onClick={() => {
                    setExtractionStatus('idle')
                    setExtractionProgress(0)
                    setSubtitles([])
                  }}
                  disabled={extractionStatus === 'processing'}
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  重置
                </Button>
              </div>

              {extractionStatus === 'error' && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 text-red-800">
                    <AlertCircle className="h-5 w-5" />
                    <span className="font-medium">提取失败</span>
                  </div>
                  <p className="text-red-700 mt-1">请检查视频文件和配置设置</p>
                </div>
              )}

              {extractionStatus === 'completed' && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 text-green-800">
                    <CheckCircle2 className="h-5 w-5" />
                    <span className="font-medium">提取完成</span>
                  </div>
                  <div className="text-green-700 mt-2 space-y-1">
                    <p>成功提取 {subtitles.length} 条字幕</p>
                    {subtitles.length > 0 && (() => {
                      const stats = SubtitleProcessor.getStatistics(subtitles)
                      return (
                        <div className="text-sm">
                          <p>总时长: {stats.totalDuration.toFixed(1)}秒</p>
                          <p>平均置信度: {(stats.averageConfidence * 100).toFixed(1)}%</p>
                          <p>总字符数: {stats.textLength}</p>
                        </div>
                      )
                    })()}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 结果预览标签页 */}
        <TabsContent value="result" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">字幕预览</h3>
            <div className="flex gap-2">
              <Button
                onClick={handleExportSubtitles}
                disabled={subtitles.length === 0}
                variant="outline"
              >
                <Download className="h-4 w-4 mr-2" />
                导出字幕
              </Button>
              <Button
                onClick={() => setSubtitles([])}
                disabled={subtitles.length === 0}
                variant="outline"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                清空
              </Button>
            </div>
          </div>

          <Card>
            <CardContent className="p-0">
              <ScrollArea className="h-96">
                {subtitles.length === 0 ? (
                  <div className="p-8 text-center text-gray-500">
                    <FileText className="mx-auto h-12 w-12 mb-4" />
                    <p>暂无字幕数据</p>
                    <p className="text-sm">请先上传视频并开始提取</p>
                  </div>
                ) : (
                  <div className="divide-y">
                    {subtitles.map((subtitle, index) => (
                      <div
                        key={subtitle.id}
                        className={`p-4 hover:bg-gray-50 cursor-pointer ${
                          selectedSubtitle?.id === subtitle.id ? 'bg-blue-50' : ''
                        }`}
                        onClick={() => setSelectedSubtitle(subtitle)}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <span className="text-sm font-medium">#{index + 1}</span>
                          <Badge variant="secondary">
                            {(subtitle.confidence * 100).toFixed(0)}%
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-600 mb-1">
                          {formatTime(subtitle.startTime)} → {formatTime(subtitle.endTime)}
                        </div>
                        <div className="text-sm">{subtitle.text}</div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

        {/* 隐藏的canvas用于帧处理 */}
        <canvas ref={canvasRef} className="hidden" />
      </div>
    </div>
  )
}
