// 字幕后处理工具类

export interface SubtitleItem {
  id: string
  startTime: number
  endTime: number
  text: string
  confidence: number
  frameIndex: number
}

export interface ProcessingConfig {
  minDuration: number // 最小持续时间（秒）
  maxDuration: number // 最大持续时间（秒）
  mergeSimilar: boolean // 合并相似字幕
  removeEmpty: boolean // 移除空字幕
  similarityThreshold: number // 相似度阈值
  timeGapThreshold: number // 时间间隔阈值（秒）
}

export class SubtitleProcessor {
  // 字幕后处理主函数
  static processSubtitles(
    subtitles: SubtitleItem[], 
    config: ProcessingConfig
  ): SubtitleItem[] {
    let processed = [...subtitles]
    
    // 1. 移除空字幕
    if (config.removeEmpty) {
      processed = this.removeEmptySubtitles(processed)
    }
    
    // 2. 按时间排序
    processed = this.sortByTime(processed)
    
    // 3. 合并相似字幕
    if (config.mergeSimilar) {
      processed = this.mergeSimilarSubtitles(processed, config.similarityThreshold, config.timeGapThreshold)
    }
    
    // 4. 过滤持续时间
    processed = this.filterByDuration(processed, config.minDuration, config.maxDuration)
    
    // 5. 修复时间轴重叠
    processed = this.fixTimeOverlaps(processed)
    
    // 6. 重新分配ID
    processed = this.reassignIds(processed)
    
    return processed
  }

  // 移除空字幕
  static removeEmptySubtitles(subtitles: SubtitleItem[]): SubtitleItem[] {
    return subtitles.filter(subtitle => 
      subtitle.text && 
      subtitle.text.trim().length > 0 &&
      subtitle.text.trim() !== '...' &&
      subtitle.text.trim() !== '—' &&
      subtitle.text.trim() !== '-'
    )
  }

  // 按时间排序
  static sortByTime(subtitles: SubtitleItem[]): SubtitleItem[] {
    return subtitles.sort((a, b) => a.startTime - b.startTime)
  }

  // 合并相似字幕
  static mergeSimilarSubtitles(
    subtitles: SubtitleItem[], 
    similarityThreshold: number,
    timeGapThreshold: number
  ): SubtitleItem[] {
    if (subtitles.length <= 1) return subtitles

    const merged: SubtitleItem[] = []
    let current = { ...subtitles[0] }

    for (let i = 1; i < subtitles.length; i++) {
      const next = subtitles[i]
      const timeGap = next.startTime - current.endTime
      const similarity = this.calculateTextSimilarity(current.text, next.text)

      // 如果相似度高且时间间隔小，则合并
      if (similarity >= similarityThreshold && timeGap <= timeGapThreshold) {
        // 合并字幕
        current.endTime = Math.max(current.endTime, next.endTime)
        current.confidence = Math.max(current.confidence, next.confidence)
        
        // 如果下一个字幕的文本更长或置信度更高，使用它的文本
        if (next.text.length > current.text.length || next.confidence > current.confidence) {
          current.text = next.text
        }
      } else {
        // 不合并，保存当前字幕并开始新的
        merged.push(current)
        current = { ...next }
      }
    }

    // 添加最后一个字幕
    merged.push(current)
    return merged
  }

  // 计算文本相似度
  static calculateTextSimilarity(text1: string, text2: string): number {
    if (!text1 || !text2) return 0
    if (text1 === text2) return 1

    // 清理文本
    const clean1 = this.cleanText(text1)
    const clean2 = this.cleanText(text2)
    
    if (clean1 === clean2) return 1

    // 使用编辑距离计算相似度
    const distance = this.levenshteinDistance(clean1, clean2)
    const maxLength = Math.max(clean1.length, clean2.length)
    
    return maxLength > 0 ? 1 - (distance / maxLength) : 0
  }

  // 清理文本
  static cleanText(text: string): string {
    return text
      .trim()
      .toLowerCase()
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '') // 只保留中文、英文和数字
  }

  // 计算编辑距离
  static levenshteinDistance(str1: string, str2: string): number {
    const matrix: number[][] = []
    const len1 = str1.length
    const len2 = str2.length

    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i]
    }

    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j
    }

    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        if (str1[i - 1] === str2[j - 1]) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j] + 1,     // 删除
            matrix[i][j - 1] + 1,     // 插入
            matrix[i - 1][j - 1] + 1  // 替换
          )
        }
      }
    }

    return matrix[len1][len2]
  }

  // 按持续时间过滤
  static filterByDuration(
    subtitles: SubtitleItem[], 
    minDuration: number, 
    maxDuration: number
  ): SubtitleItem[] {
    return subtitles.filter(subtitle => {
      const duration = subtitle.endTime - subtitle.startTime
      return duration >= minDuration && duration <= maxDuration
    })
  }

  // 修复时间轴重叠
  static fixTimeOverlaps(subtitles: SubtitleItem[]): SubtitleItem[] {
    if (subtitles.length <= 1) return subtitles

    const fixed: SubtitleItem[] = []
    let current = { ...subtitles[0] }

    for (let i = 1; i < subtitles.length; i++) {
      const next = { ...subtitles[i] }

      // 如果当前字幕的结束时间超过下一个字幕的开始时间
      if (current.endTime > next.startTime) {
        // 调整当前字幕的结束时间
        const midPoint = (current.endTime + next.startTime) / 2
        current.endTime = midPoint
        next.startTime = midPoint
      }

      fixed.push(current)
      current = next
    }

    // 添加最后一个字幕
    fixed.push(current)
    return fixed
  }

  // 重新分配ID
  static reassignIds(subtitles: SubtitleItem[]): SubtitleItem[] {
    return subtitles.map((subtitle, index) => ({
      ...subtitle,
      id: `subtitle_${index + 1}`
    }))
  }

  // 字幕去重
  static removeDuplicates(subtitles: SubtitleItem[]): SubtitleItem[] {
    const seen = new Set<string>()
    return subtitles.filter(subtitle => {
      const key = `${subtitle.text}_${subtitle.startTime}_${subtitle.endTime}`
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }

  // 时间轴校正
  static adjustTimeline(subtitles: SubtitleItem[], offset: number): SubtitleItem[] {
    return subtitles.map(subtitle => ({
      ...subtitle,
      startTime: Math.max(0, subtitle.startTime + offset),
      endTime: Math.max(0, subtitle.endTime + offset)
    }))
  }

  // 字幕统计信息
  static getStatistics(subtitles: SubtitleItem[]): {
    totalCount: number
    totalDuration: number
    averageDuration: number
    averageConfidence: number
    textLength: number
  } {
    if (subtitles.length === 0) {
      return {
        totalCount: 0,
        totalDuration: 0,
        averageDuration: 0,
        averageConfidence: 0,
        textLength: 0
      }
    }

    const totalDuration = subtitles.reduce((sum, sub) => sum + (sub.endTime - sub.startTime), 0)
    const averageConfidence = subtitles.reduce((sum, sub) => sum + sub.confidence, 0) / subtitles.length
    const textLength = subtitles.reduce((sum, sub) => sum + sub.text.length, 0)

    return {
      totalCount: subtitles.length,
      totalDuration,
      averageDuration: totalDuration / subtitles.length,
      averageConfidence,
      textLength
    }
  }

  // 验证字幕数据
  static validateSubtitles(subtitles: SubtitleItem[]): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    for (let i = 0; i < subtitles.length; i++) {
      const subtitle = subtitles[i]

      // 检查时间有效性
      if (subtitle.startTime < 0) {
        errors.push(`字幕 ${i + 1}: 开始时间不能为负数`)
      }

      if (subtitle.endTime <= subtitle.startTime) {
        errors.push(`字幕 ${i + 1}: 结束时间必须大于开始时间`)
      }

      // 检查文本有效性
      if (!subtitle.text || subtitle.text.trim().length === 0) {
        errors.push(`字幕 ${i + 1}: 文本内容为空`)
      }

      // 检查置信度
      if (subtitle.confidence < 0 || subtitle.confidence > 1) {
        errors.push(`字幕 ${i + 1}: 置信度必须在0-1之间`)
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}
