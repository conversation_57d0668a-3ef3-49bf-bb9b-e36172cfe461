# PP-OCRv5 模型文件

请将您的PP-OCRv5 ONNX模型文件放置在此目录下：

## 必需文件

1. **det.onnx** - 文字检测模型
   - 用于检测图像中的文字区域
   - 输入: 图像数据 (1, 3, 640, 640)
   - 输出: 检测框坐标

2. **rec.onnx** - 文字识别模型
   - 用于识别检测到的文字内容
   - 输入: 裁剪的文字图像 (1, 3, 32, 320)
   - 输出: 字符概率分布

3. **dict.txt** - 字符字典文件
   - 包含模型可识别的所有字符
   - 每行一个字符
   - 用于将模型输出转换为文字

## 模型来源

这些模型文件应该从以下位置获取：
- D:\.background\modle 文件夹
- 或从PaddleOCR官方下载并转换为ONNX格式

## 模型文件复制命令

请在命令行中执行以下命令将模型文件复制到项目中：

```bash
# Windows
copy "D:\.background\modle\*" "models\"

# 或者手动复制以下文件：
# D:\.background\modle\det.onnx -> models/det.onnx
# D:\.background\modle\rec.onnx -> models/rec.onnx
# D:\.background\modle\dict.txt -> models/dict.txt
```

## 使用说明

1. 确保所有三个文件都存在于此目录
2. 文件名必须完全匹配（区分大小写）
3. 模型文件较大，请确保有足够的磁盘空间
4. 首次加载模型时可能需要较长时间

## 故障排除

如果遇到模型加载问题：
1. 检查文件是否存在且完整
2. 确认ONNX Runtime版本兼容性
3. 查看服务器日志获取详细错误信息
4. 确保模型文件是PP-OCRv5的ONNX格式

## 技术细节

- 检测模型使用DBNet架构
- 识别模型使用CRNN+CTC架构
- 支持中文、英文等多语言识别
- 模型输入需要进行标准化预处理
