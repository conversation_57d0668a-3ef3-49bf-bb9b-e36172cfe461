// 视频处理工具类
export class VideoProcessor {
  // 从视频中提取帧
  static async extractFrames(
    videoFile: File,
    config: {
      startTime: number
      endTime: number
      frameInterval: number
      subtitleRegion: {
        x: number
        y: number
        width: number
        height: number
      }
    }
  ): Promise<Array<{ blob: Blob; timestamp: number; canvas: HTMLCanvasElement }>> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video')
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      if (!ctx) {
        reject(new Error('无法创建Canvas上下文'))
        return
      }

      const frames: Array<{ blob: Blob; timestamp: number; canvas: HTMLCanvasElement }> = []
      let currentTime = config.startTime
      
      video.onloadedmetadata = () => {
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight
        
        // 开始提取帧
        extractNextFrame()
      }

      video.onerror = () => {
        reject(new Error('视频加载失败'))
      }

      const extractNextFrame = () => {
        if (currentTime >= config.endTime) {
          resolve(frames)
          return
        }

        video.currentTime = currentTime
        
        video.onseeked = () => {
          // 绘制当前帧到canvas
          ctx.clearRect(0, 0, canvas.width, canvas.height)
          ctx.drawImage(video, 0, 0)
          
          // 裁剪字幕区域
          const croppedCanvas = this.cropSubtitleRegion(canvas, config.subtitleRegion)
          
          // 转换为Blob
          croppedCanvas.toBlob((blob) => {
            if (blob) {
              frames.push({
                blob,
                timestamp: currentTime,
                canvas: croppedCanvas
              })
            }
            
            // 继续下一帧
            currentTime += config.frameInterval
            extractNextFrame()
          }, 'image/jpeg', 0.8)
        }
      }

      // 设置视频源
      video.src = URL.createObjectURL(videoFile)
    })
  }

  // 裁剪字幕区域
  static cropSubtitleRegion(
    sourceCanvas: HTMLCanvasElement,
    region: { x: number; y: number; width: number; height: number }
  ): HTMLCanvasElement {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      throw new Error('无法创建Canvas上下文')
    }

    const sourceWidth = sourceCanvas.width
    const sourceHeight = sourceCanvas.height
    
    // 计算实际像素位置
    const x = Math.floor((region.x / 100) * sourceWidth)
    const y = Math.floor((region.y / 100) * sourceHeight)
    const width = Math.floor((region.width / 100) * sourceWidth)
    const height = Math.floor((region.height / 100) * sourceHeight)
    
    canvas.width = width
    canvas.height = height
    
    // 获取源图像数据
    const sourceCtx = sourceCanvas.getContext('2d')
    if (!sourceCtx) {
      throw new Error('无法获取源Canvas上下文')
    }
    
    const imageData = sourceCtx.getImageData(x, y, width, height)
    ctx.putImageData(imageData, 0, 0)
    
    return canvas
  }

  // 预处理图像用于OCR
  static preprocessImageForOCR(canvas: HTMLCanvasElement): HTMLCanvasElement {
    const processedCanvas = document.createElement('canvas')
    const ctx = processedCanvas.getContext('2d')
    
    if (!ctx) {
      throw new Error('无法创建Canvas上下文')
    }

    processedCanvas.width = canvas.width
    processedCanvas.height = canvas.height
    
    // 复制原始图像
    ctx.drawImage(canvas, 0, 0)
    
    // 获取图像数据
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
    const data = imageData.data
    
    // 图像预处理：转换为灰度并增强对比度
    for (let i = 0; i < data.length; i += 4) {
      // 转换为灰度
      const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2])
      
      // 增强对比度（简单的阈值处理）
      const enhanced = gray > 128 ? 255 : 0
      
      data[i] = enhanced     // R
      data[i + 1] = enhanced // G
      data[i + 2] = enhanced // B
      // Alpha通道保持不变
    }
    
    // 应用处理后的图像数据
    ctx.putImageData(imageData, 0, 0)
    
    return processedCanvas
  }

  // 将Canvas转换为Base64
  static canvasToBase64(canvas: HTMLCanvasElement): string {
    return canvas.toDataURL('image/jpeg', 0.8)
  }

  // 将Blob转换为Base64
  static blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        resolve(result)
      }
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  // 获取视频信息
  static getVideoInfo(videoFile: File): Promise<{
    duration: number
    width: number
    height: number
    fps: number
  }> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video')
      
      video.onloadedmetadata = () => {
        resolve({
          duration: video.duration,
          width: video.videoWidth,
          height: video.videoHeight,
          fps: 30 // 默认帧率，实际应该从视频元数据获取
        })
        
        // 清理
        URL.revokeObjectURL(video.src)
      }

      video.onerror = () => {
        reject(new Error('无法加载视频文件'))
      }

      video.src = URL.createObjectURL(videoFile)
    })
  }

  // 计算文本相似度
  static calculateTextSimilarity(text1: string, text2: string): number {
    if (!text1 || !text2) return 0
    
    const len1 = text1.length
    const len2 = text2.length
    const maxLen = Math.max(len1, len2)
    
    if (maxLen === 0) return 1
    
    // 简单的编辑距离算法
    const matrix: number[][] = []
    
    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j
    }
    
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        if (text1[i - 1] === text2[j - 1]) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j - 1] + 1
          )
        }
      }
    }
    
    const distance = matrix[len1][len2]
    return 1 - (distance / maxLen)
  }

  // 格式化时间（SRT格式）
  static formatTimeForSRT(seconds: number): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`
  }

  // 格式化时间（VTT格式）
  static formatTimeForVTT(seconds: number): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
  }
}
