# 硬字幕提取功能使用说明

## 功能概述

硬字幕提取功能可以从视频文件中自动识别和提取硬编码的字幕文本，支持中文识别，基于PP-OCRv5 ONNX模型实现。

## 功能特性

### 🎯 核心功能
- **视频帧提取**: 自动从视频中提取指定时间段的帧
- **字幕区域检测**: 智能识别视频中的字幕区域
- **OCR文字识别**: 使用PP-OCRv5模型进行高精度中文识别
- **字幕后处理**: 自动去重、合并相似字幕、时间轴校正

### 📊 支持格式
- **输入视频**: MP4, AVI, MOV, MKV等常见格式
- **输出字幕**: SRT, VTT, ASS, TXT格式

### ⚙️ 配置选项
- **时间范围**: 自定义提取的开始和结束时间
- **帧间隔**: 调整提取帧的时间间隔
- **字幕区域**: 自定义字幕在视频中的位置和大小
- **OCR参数**: 置信度阈值、相似度合并等

## 使用步骤

### 1. 模型准备
在使用功能前，需要先配置PP-OCRv5模型：

```bash
# 将模型文件复制到项目目录
copy "D:\.background\modle\*" "models\"

# 或手动复制以下文件：
# D:\.background\modle\det.onnx -> models/det.onnx
# D:\.background\modle\rec.onnx -> models/rec.onnx  
# D:\.background\modle\dict.txt -> models/dict.txt
```

### 2. 启动服务
```bash
npm run dev
```

### 3. 访问功能
1. 打开分集简介页面
2. 点击左侧菜单中的"硬字幕提取"按钮
3. 在弹出的对话框中进行操作

### 4. 操作流程

#### 步骤1: 视频上传
- 点击"选择文件"或拖拽视频文件到上传区域
- 支持的格式：MP4, AVI, MOV, MKV等
- 系统会自动获取视频信息（时长、分辨率等）

#### 步骤2: 提取配置
- **视频处理配置**:
  - 开始时间：设置提取的起始时间点
  - 结束时间：设置提取的结束时间点
  - 帧间隔：设置每隔多少秒提取一帧（建议1-2秒）

- **OCR识别配置**:
  - 置信度阈值：设置OCR识别的最低置信度（建议0.7-0.9）
  - 字幕区域：设置字幕在视频中的位置（X%, Y%, 宽度%, 高度%）

- **字幕处理配置**:
  - 最小持续时间：过滤掉太短的字幕
  - 最大持续时间：过滤掉太长的字幕
  - 合并相似字幕：自动合并内容相似的连续字幕
  - 移除空字幕：自动删除空白或无效的字幕

- **输出配置**:
  - 输出格式：选择SRT、VTT、ASS或TXT格式
  - 文件编码：选择UTF-8或GBK编码

#### 步骤3: 开始提取
- 点击"开始提取"按钮
- 系统会显示实时进度
- 提取过程包括：帧提取 → OCR识别 → 后处理

#### 步骤4: 结果预览
- 查看提取到的字幕列表
- 每条字幕显示时间轴、文本内容和置信度
- 可以点击单条字幕进行预览

#### 步骤5: 导出字幕
- 点击"导出字幕"按钮
- 选择的格式和编码会自动应用
- 文件会自动下载到本地

## 配置建议

### 字幕区域设置
- **X位置**: 通常设置为10-20%（字幕一般居中偏下）
- **Y位置**: 通常设置为70-85%（字幕在视频下方）
- **宽度**: 通常设置为60-80%（覆盖大部分字幕区域）
- **高度**: 通常设置为15-25%（足够覆盖字幕高度）

### 性能优化
- **帧间隔**: 
  - 快速预览：2-3秒
  - 标准质量：1-1.5秒
  - 高质量：0.5-1秒

- **置信度阈值**:
  - 宽松模式：0.6-0.7（更多结果，可能有误识别）
  - 标准模式：0.7-0.8（平衡准确性和召回率）
  - 严格模式：0.8-0.9（高准确性，可能遗漏部分字幕）

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查models目录下是否有det.onnx、rec.onnx、dict.txt文件
   - 确认文件完整性和权限
   - 查看控制台错误信息

2. **OCR识别效果差**
   - 调整字幕区域设置，确保完全覆盖字幕
   - 降低置信度阈值
   - 检查视频质量和字幕清晰度

3. **提取速度慢**
   - 增大帧间隔
   - 缩短提取时间范围
   - 减小字幕区域大小

4. **字幕重复或错乱**
   - 启用"合并相似字幕"选项
   - 调整相似度阈值
   - 检查时间轴设置

### 测试工具
```bash
# 运行OCR测试脚本
npm run test:ocr
```

## 技术细节

### 模型信息
- **检测模型**: PP-OCRv5 DBNet，用于检测文字区域
- **识别模型**: PP-OCRv5 CRNN+CTC，用于识别文字内容
- **字典文件**: 包含中文字符集的字典

### 处理流程
1. **视频解析**: 使用HTML5 Video API获取视频信息
2. **帧提取**: 使用Canvas API提取视频帧
3. **图像预处理**: 裁剪字幕区域，调整尺寸和格式
4. **OCR识别**: 调用PP-OCRv5模型进行文字识别
5. **后处理**: 去重、合并、时间轴校正等

### 性能指标
- **识别准确率**: 中文字幕 > 90%
- **处理速度**: 约1-2秒/帧（取决于硬件配置）
- **支持分辨率**: 最高4K视频

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的硬字幕提取功能
- 集成PP-OCRv5 ONNX模型
- 支持多种输出格式
