import * as ort from 'onnxruntime-node'
import sharp from 'sharp'

// 检测结果接口
interface DetectionBox {
  x1: number
  y1: number
  x2: number
  y2: number
  x3: number
  y3: number
  x4: number
  y4: number
  confidence: number
}

// 识别结果接口
interface RecognitionResult {
  text: string
  confidence: number
}

// 文字识别
export async function recognizeTextInRegions(
  imageBuffer: Buffer,
  detectionBoxes: DetectionBox[],
  recSession: ort.InferenceSession,
  dictionary: string[]
): Promise<RecognitionResult[]> {
  try {
    const results: RecognitionResult[] = []
    
    for (const box of detectionBoxes) {
      try {
        // 裁剪文字区域
        const croppedImage = await cropTextRegion(imageBuffer, box)
        
        // 预处理用于识别
        const preprocessedData = await preprocessImageForRecognition(croppedImage)
        
        // 运行识别模型
        const recResults = await recSession.run({ x: preprocessedData.tensor })
        
        // 解析识别结果
        const outputKey = Object.keys(recResults)[0]
        const output = recResults[outputKey]
        
        if (output && output.data) {
          const text = decodeRecognitionResult(output.data as Float32Array, output.dims as number[], dictionary)
          if (text.trim()) {
            results.push({
              text: text.trim(),
              confidence: calculateTextConfidence(output.data as Float32Array)
            })
          }
        }
      } catch (error) {
        console.warn('单个文字区域识别失败:', error)
      }
    }
    
    return results
  } catch (error) {
    console.error('文字识别失败:', error)
    throw new Error('文字识别失败')
  }
}

// 裁剪文字区域
async function cropTextRegion(imageBuffer: Buffer, box: DetectionBox): Promise<Buffer> {
  try {
    // 计算边界框
    const minX = Math.max(0, Math.floor(Math.min(box.x1, box.x2, box.x3, box.x4)))
    const minY = Math.max(0, Math.floor(Math.min(box.y1, box.y2, box.y3, box.y4)))
    const maxX = Math.ceil(Math.max(box.x1, box.x2, box.x3, box.x4))
    const maxY = Math.ceil(Math.max(box.y1, box.y2, box.y3, box.y4))
    
    const width = maxX - minX
    const height = maxY - minY
    
    if (width <= 0 || height <= 0) {
      throw new Error('无效的裁剪区域')
    }
    
    // 使用sharp裁剪图像
    const croppedBuffer = await sharp(imageBuffer)
      .extract({ left: minX, top: minY, width, height })
      .toBuffer()
    
    return croppedBuffer
  } catch (error) {
    console.error('图像裁剪失败:', error)
    throw new Error('图像裁剪失败')
  }
}

// 图像预处理（用于识别）
async function preprocessImageForRecognition(imageBuffer: Buffer): Promise<{ tensor: ort.Tensor }> {
  try {
    // PP-OCRv5识别模型的标准输入尺寸
    const targetHeight = 32
    const targetWidth = 320
    
    // 获取原始图像尺寸
    const { width: originalWidth, height: originalHeight } = await sharp(imageBuffer).metadata()
    
    if (!originalWidth || !originalHeight) {
      throw new Error('无法获取图像尺寸')
    }
    
    // 计算缩放比例，保持宽高比
    const scale = Math.min(targetWidth / originalWidth, targetHeight / originalHeight)
    const resizedWidth = Math.round(originalWidth * scale)
    const resizedHeight = Math.round(originalHeight * scale)
    
    // 调整图像大小
    const { data } = await sharp(imageBuffer)
      .resize(resizedWidth, resizedHeight, { fit: 'inside' })
      .removeAlpha()
      .raw()
      .toBuffer({ resolveWithObject: true })
    
    // 创建目标尺寸的画布
    const canvas = new Float32Array(3 * targetHeight * targetWidth)
    canvas.fill(0) // 填充黑色背景
    
    // 计算居中位置
    const offsetX = Math.floor((targetWidth - resizedWidth) / 2)
    const offsetY = Math.floor((targetHeight - resizedHeight) / 2)
    
    // 将图像数据复制到画布上，转换为CHW格式并归一化
    for (let y = 0; y < resizedHeight; y++) {
      for (let x = 0; x < resizedWidth; x++) {
        const srcIdx = (y * resizedWidth + x) * 3
        const dstX = x + offsetX
        const dstY = y + offsetY
        
        if (dstX < targetWidth && dstY < targetHeight) {
          // RGB -> CHW格式，并归一化到[0,1]
          canvas[0 * targetHeight * targetWidth + dstY * targetWidth + dstX] = data[srcIdx] / 255.0     // R
          canvas[1 * targetHeight * targetWidth + dstY * targetWidth + dstX] = data[srcIdx + 1] / 255.0 // G
          canvas[2 * targetHeight * targetWidth + dstY * targetWidth + dstX] = data[srcIdx + 2] / 255.0 // B
        }
      }
    }
    
    const tensor = new ort.Tensor('float32', canvas, [1, 3, targetHeight, targetWidth])
    
    return { tensor }
  } catch (error) {
    console.error('识别图像预处理失败:', error)
    throw new Error('识别图像预处理失败')
  }
}

// 解码识别结果
function decodeRecognitionResult(outputData: Float32Array, outputDims: number[], dictionary: string[]): string {
  try {
    // PP-OCRv5识别模型输出格式：[batch_size, sequence_length, vocab_size]
    const [batchSize, sequenceLength, vocabSize] = outputDims
    
    let text = ''
    let lastChar = ''
    
    // CTC解码：选择每个时间步概率最高的字符
    for (let t = 0; t < sequenceLength; t++) {
      let maxProb = -1
      let maxIndex = 0
      
      // 找到概率最高的字符索引
      for (let c = 0; c < Math.min(vocabSize, dictionary.length); c++) {
        const prob = outputData[t * vocabSize + c]
        if (prob > maxProb) {
          maxProb = prob
          maxIndex = c
        }
      }
      
      // CTC解码规则：
      // 1. 跳过空白字符（索引0）
      // 2. 跳过连续重复的字符
      if (maxIndex > 0 && maxIndex < dictionary.length) {
        const currentChar = dictionary[maxIndex]
        if (currentChar !== lastChar) {
          text += currentChar
          lastChar = currentChar
        }
      } else {
        lastChar = ''
      }
    }
    
    return text
  } catch (error) {
    console.error('识别结果解码失败:', error)
    return ''
  }
}

// 计算文本置信度
function calculateTextConfidence(outputData: Float32Array): number {
  try {
    // 简单的置信度计算：取所有概率的平均值
    let sum = 0
    let count = 0
    
    for (let i = 0; i < outputData.length; i++) {
      if (outputData[i] > 0) {
        sum += outputData[i]
        count++
      }
    }
    
    return count > 0 ? Math.min(sum / count, 1.0) : 0
  } catch (error) {
    console.error('置信度计算失败:', error)
    return 0
  }
}
