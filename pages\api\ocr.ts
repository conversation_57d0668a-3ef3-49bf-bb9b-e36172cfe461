import { NextApiRequest, NextApiResponse } from 'next'
import * as ort from 'onnxruntime-node'
import fs from 'fs/promises'
import path from 'path'
import sharp from 'sharp'
import { recognizeTextInRegions } from '@/lib/ocr-recognition'

// OCR模型配置
interface OCRConfig {
  detModelPath: string
  recModelPath: string
  dictPath: string
}

// OCR结果接口
interface OCRResult {
  success: boolean
  text?: string
  confidence?: number
  details?: any
  error?: string
}

// 检测结果接口
interface DetectionBox {
  x1: number
  y1: number
  x2: number
  y2: number
  x3: number
  y3: number
  x4: number
  y4: number
  confidence: number
}

// 全局变量存储已加载的模型
let ocrModels: {
  detSession?: ort.InferenceSession
  recSession?: ort.InferenceSession
  dictionary?: string[]
} = {}

// 模型路径配置 - 指向您的模型文件夹
const MODEL_CONFIG: OCRConfig = {
  detModelPath: path.join(process.cwd(), 'models', 'det.onnx'),
  recModelPath: path.join(process.cwd(), 'models', 'rec.onnx'),
  dictPath: path.join(process.cwd(), 'models', 'dict.txt')
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<OCRResult>) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    })
  }

  try {
    const { imagePath, imageData } = req.body

    if (!imagePath && !imageData) {
      return res.status(400).json({
        success: false,
        error: '缺少图像路径或图像数据'
      })
    }

    // 初始化OCR模型（如果尚未加载）
    await initializeOCRModels()

    // 处理图像
    let processedImage: Buffer
    if (imagePath) {
      // 从文件路径读取图像
      processedImage = await fs.readFile(imagePath)
    } else {
      // 从base64数据解析图像
      const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '')
      processedImage = Buffer.from(base64Data, 'base64')
    }

    // 真实的PP-OCRv5处理流程
    const ocrResult = await performRealOCR(processedImage)

    return res.status(200).json({
      success: true,
      text: ocrResult.text,
      confidence: ocrResult.confidence,
      details: ocrResult.details
    })

  } catch (error) {
    console.error('OCR处理失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    })
  }
}

// 初始化OCR模型
async function initializeOCRModels(): Promise<void> {
  try {
    // 检查模型是否已加载
    if (ocrModels.detSession && ocrModels.recSession && ocrModels.dictionary) {
      return
    }

    console.log('正在加载PP-OCRv5模型...')

    // 加载检测模型
    if (!ocrModels.detSession) {
      try {
        const detModelBuffer = await fs.readFile(MODEL_CONFIG.detModelPath)
        ocrModels.detSession = await ort.InferenceSession.create(detModelBuffer)
        console.log('文字检测模型加载完成')
      } catch (error) {
        throw new Error(`检测模型加载失败: ${error}`)
      }
    }

    // 加载识别模型
    if (!ocrModels.recSession) {
      try {
        const recModelBuffer = await fs.readFile(MODEL_CONFIG.recModelPath)
        ocrModels.recSession = await ort.InferenceSession.create(recModelBuffer)
        console.log('文字识别模型加载完成')
      } catch (error) {
        throw new Error(`识别模型加载失败: ${error}`)
      }
    }

    // 加载字典
    if (!ocrModels.dictionary) {
      try {
        const dictContent = await fs.readFile(MODEL_CONFIG.dictPath, 'utf-8')
        ocrModels.dictionary = dictContent.split('\n').filter(line => line.trim())
        console.log(`字典加载完成，包含 ${ocrModels.dictionary.length} 个字符`)
      } catch (error) {
        throw new Error(`字典加载失败: ${error}`)
      }
    }

  } catch (error) {
    console.error('PP-OCRv5模型加载失败:', error)
    throw new Error(`模型加载失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

// 真实的PP-OCRv5处理
async function performRealOCR(imageBuffer: Buffer): Promise<{ text: string; confidence: number; details: any }> {
  try {
    // 1. 图像预处理
    const preprocessedImage = await preprocessImageForDetection(imageBuffer)

    // 2. 文字检测
    const detectionBoxes = await detectTextRegions(preprocessedImage)

    if (!detectionBoxes || detectionBoxes.length === 0) {
      return {
        text: '',
        confidence: 0,
        details: { message: '未检测到文字区域' }
      }
    }

    // 3. 文字识别
    if (!ocrModels.recSession || !ocrModels.dictionary) {
      throw new Error('识别模型或字典未加载')
    }

    const recognitionResults = await recognizeTextInRegions(
      imageBuffer,
      detectionBoxes,
      ocrModels.recSession,
      ocrModels.dictionary
    )

    // 4. 合并结果
    const finalText = recognitionResults
      .filter(result => result.confidence > 0.5)
      .map(result => result.text)
      .join(' ')

    const avgConfidence = recognitionResults.length > 0
      ? recognitionResults.reduce((sum, result) => sum + result.confidence, 0) / recognitionResults.length
      : 0

    return {
      text: finalText,
      confidence: avgConfidence,
      details: {
        detectedRegions: detectionBoxes.length,
        recognitionResults: recognitionResults
      }
    }
  } catch (error) {
    console.error('PP-OCRv5处理失败:', error)
    throw new Error(`OCR处理失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

// 图像预处理（用于检测）
async function preprocessImageForDetection(imageBuffer: Buffer): Promise<{
  tensor: ort.Tensor,
  originalWidth: number,
  originalHeight: number,
  resizedWidth: number,
  resizedHeight: number
}> {
  try {
    // 获取原始图像尺寸
    const { width: originalWidth, height: originalHeight } = await sharp(imageBuffer).metadata()

    if (!originalWidth || !originalHeight) {
      throw new Error('无法获取图像尺寸')
    }

    // PP-OCRv5检测模型的输入尺寸通常是动态的，这里使用640x640
    const targetSize = 640

    // 计算缩放比例，保持宽高比
    const scale = Math.min(targetSize / originalWidth, targetSize / originalHeight)
    const resizedWidth = Math.round(originalWidth * scale)
    const resizedHeight = Math.round(originalHeight * scale)

    // 调整图像大小并转换为RGB
    const { data } = await sharp(imageBuffer)
      .resize(resizedWidth, resizedHeight, { fit: 'inside' })
      .removeAlpha()
      .raw()
      .toBuffer({ resolveWithObject: true })

    // 创建640x640的画布，将图像居中放置
    const canvas = new Float32Array(3 * targetSize * targetSize)
    canvas.fill(0) // 填充黑色背景

    // 计算居中位置
    const offsetX = Math.floor((targetSize - resizedWidth) / 2)
    const offsetY = Math.floor((targetSize - resizedHeight) / 2)

    // 将图像数据复制到画布上，同时进行归一化和通道重排（HWC -> CHW）
    for (let y = 0; y < resizedHeight; y++) {
      for (let x = 0; x < resizedWidth; x++) {
        const srcIdx = (y * resizedWidth + x) * 3
        const dstX = x + offsetX
        const dstY = y + offsetY

        if (dstX < targetSize && dstY < targetSize) {
          // RGB -> CHW格式，并归一化到[0,1]
          canvas[0 * targetSize * targetSize + dstY * targetSize + dstX] = data[srcIdx] / 255.0     // R
          canvas[1 * targetSize * targetSize + dstY * targetSize + dstX] = data[srcIdx + 1] / 255.0 // G
          canvas[2 * targetSize * targetSize + dstY * targetSize + dstX] = data[srcIdx + 2] / 255.0 // B
        }
      }
    }

    const tensor = new ort.Tensor('float32', canvas, [1, 3, targetSize, targetSize])

    return {
      tensor,
      originalWidth,
      originalHeight,
      resizedWidth: targetSize,
      resizedHeight: targetSize
    }
  } catch (error) {
    console.error('图像预处理失败:', error)
    throw new Error('图像预处理失败')
  }
}

// 文字检测
async function detectTextRegions(preprocessedData: {
  tensor: ort.Tensor,
  originalWidth: number,
  originalHeight: number,
  resizedWidth: number,
  resizedHeight: number
}): Promise<DetectionBox[]> {
  try {
    if (!ocrModels.detSession) {
      throw new Error('检测模型未加载')
    }

    // 运行检测模型
    const results = await ocrModels.detSession.run({ x: preprocessedData.tensor })

    // 获取输出（通常是概率图）
    const outputKey = Object.keys(results)[0]
    const output = results[outputKey]

    if (!output || !output.data) {
      throw new Error('检测模型输出无效')
    }

    // 解析检测结果
    const detectionBoxes = parseDetectionOutput(
      output.data as Float32Array,
      output.dims as number[],
      preprocessedData.originalWidth,
      preprocessedData.originalHeight,
      preprocessedData.resizedWidth,
      preprocessedData.resizedHeight
    )

    return detectionBoxes
  } catch (error) {
    console.error('文字检测失败:', error)
    throw new Error('文字检测失败')
  }
}

// 解析检测输出
function parseDetectionOutput(
  outputData: Float32Array,
  outputDims: number[],
  originalWidth: number,
  originalHeight: number,
  resizedWidth: number,
  resizedHeight: number
): DetectionBox[] {
  try {
    const detectionBoxes: DetectionBox[] = []

    // PP-OCRv5检测模型输出通常是概率图，需要进行后处理
    // 这里实现简化的后处理逻辑

    const [batchSize, channels, height, width] = outputDims
    const threshold = 0.3 // 检测阈值

    // 计算缩放比例
    const scaleX = originalWidth / resizedWidth
    const scaleY = originalHeight / resizedHeight

    // 简化的检测框提取（实际应该使用DBNet后处理）
    for (let y = 0; y < height; y += 8) { // 降采样以提高性能
      for (let x = 0; x < width; x += 8) {
        const idx = y * width + x
        if (outputData[idx] > threshold) {
          // 创建检测框（简化版本）
          const x1 = Math.max(0, (x - 16) * scaleX)
          const y1 = Math.max(0, (y - 8) * scaleY)
          const x2 = Math.min(originalWidth, (x + 32) * scaleX)
          const y2 = Math.min(originalHeight, (y + 16) * scaleY)

          // 检查框的大小是否合理
          if (x2 - x1 > 10 && y2 - y1 > 5) {
            detectionBoxes.push({
              x1, y1, x2, y1, // 上边
              x2, y2, x1, y2, // 下边
              confidence: outputData[idx]
            })
          }
        }
      }
    }

    // 简单的非极大值抑制
    return nonMaxSuppression(detectionBoxes, 0.5)
  } catch (error) {
    console.error('检测结果解析失败:', error)
    return []
  }
}

// 简单的非极大值抑制
function nonMaxSuppression(boxes: DetectionBox[], iouThreshold: number): DetectionBox[] {
  if (boxes.length === 0) return []

  // 按置信度排序
  boxes.sort((a, b) => b.confidence - a.confidence)

  const keep: DetectionBox[] = []
  const suppressed = new Set<number>()

  for (let i = 0; i < boxes.length; i++) {
    if (suppressed.has(i)) continue

    keep.push(boxes[i])

    for (let j = i + 1; j < boxes.length; j++) {
      if (suppressed.has(j)) continue

      const iou = calculateIOU(boxes[i], boxes[j])
      if (iou > iouThreshold) {
        suppressed.add(j)
      }
    }
  }

  return keep
}

// 计算两个框的IoU
function calculateIOU(box1: DetectionBox, box2: DetectionBox): number {
  const x1 = Math.max(Math.min(box1.x1, box1.x2, box1.x3, box1.x4), Math.min(box2.x1, box2.x2, box2.x3, box2.x4))
  const y1 = Math.max(Math.min(box1.y1, box1.y2, box1.y3, box1.y4), Math.min(box2.y1, box2.y2, box2.y3, box2.y4))
  const x2 = Math.min(Math.max(box1.x1, box1.x2, box1.x3, box1.x4), Math.max(box2.x1, box2.x2, box2.x3, box2.x4))
  const y2 = Math.min(Math.max(box1.y1, box1.y2, box1.y3, box1.y4), Math.max(box2.y1, box2.y2, box2.y3, box2.y4))

  if (x2 <= x1 || y2 <= y1) return 0

  const intersection = (x2 - x1) * (y2 - y1)
  const area1 = Math.abs((box1.x1 - box1.x3) * (box1.y1 - box1.y3))
  const area2 = Math.abs((box2.x1 - box2.x3) * (box2.y1 - box2.y3))
  const union = area1 + area2 - intersection

  return union > 0 ? intersection / union : 0
}
