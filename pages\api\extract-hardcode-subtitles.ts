import { NextApiRequest, NextApiResponse } from 'next'
import formidable from 'formidable'
import fs from 'fs/promises'
import path from 'path'
import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

// 禁用默认的body parser，因为我们需要处理文件上传
export const config = {
  api: {
    bodyParser: false,
  },
}

interface ExtractionConfig {
  startTime: number
  endTime: number
  frameInterval: number
  ocrConfidence: number
  subtitleRegion: {
    x: number
    y: number
    width: number
    height: number
  }
  minDuration: number
  maxDuration: number
  mergeSimilar: boolean
  removeEmpty: boolean
  outputFormat: 'srt' | 'vtt' | 'ass' | 'txt'
  encoding: 'utf-8' | 'gbk'
}

interface SubtitleItem {
  id: string
  startTime: number
  endTime: number
  text: string
  confidence: number
  frameIndex: number
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // 解析表单数据
    const form = formidable({
      uploadDir: path.join(process.cwd(), 'temp'),
      keepExtensions: true,
      maxFileSize: 500 * 1024 * 1024, // 500MB
    })

    const [fields, files] = await form.parse(req)
    
    const videoFile = Array.isArray(files.video) ? files.video[0] : files.video
    const configStr = Array.isArray(fields.config) ? fields.config[0] : fields.config

    if (!videoFile || !configStr) {
      return res.status(400).json({ error: '缺少视频文件或配置' })
    }

    const config: ExtractionConfig = JSON.parse(configStr)
    
    // 创建临时工作目录
    const sessionId = Date.now().toString()
    const workDir = path.join(process.cwd(), 'temp', `ocr_session_${sessionId}`)
    await fs.mkdir(workDir, { recursive: true })

    try {
      // 提取视频帧
      const frames = await extractVideoFrames(videoFile.filepath, config, workDir)
      
      // 对每一帧进行OCR识别
      const subtitles: SubtitleItem[] = []
      
      for (let i = 0; i < frames.length; i++) {
        const frame = frames[i]
        
        // 裁剪字幕区域
        const croppedFrame = await cropSubtitleRegion(frame.path, config.subtitleRegion, workDir)
        
        // OCR识别
        const ocrResult = await performOCR(croppedFrame)
        
        if (ocrResult.text && ocrResult.confidence >= config.ocrConfidence) {
          // 检查是否与前一条字幕相似（如果启用合并）
          const lastSubtitle = subtitles[subtitles.length - 1]
          const shouldMerge = config.mergeSimilar && 
                            lastSubtitle && 
                            calculateSimilarity(lastSubtitle.text, ocrResult.text) > 0.8

          if (shouldMerge) {
            // 合并到前一条字幕，延长结束时间
            lastSubtitle.endTime = frame.timestamp
          } else {
            // 创建新的字幕条目
            const duration = config.frameInterval
            if (duration >= config.minDuration && duration <= config.maxDuration) {
              subtitles.push({
                id: `subtitle_${i}`,
                startTime: frame.timestamp,
                endTime: frame.timestamp + duration,
                text: ocrResult.text,
                confidence: ocrResult.confidence,
                frameIndex: i
              })
            }
          }
        }
      }

      // 后处理：移除空字幕
      const finalSubtitles = config.removeEmpty 
        ? subtitles.filter(sub => sub.text.trim().length > 0)
        : subtitles

      res.status(200).json({
        success: true,
        subtitles: finalSubtitles,
        totalFrames: frames.length,
        sessionId
      })

    } finally {
      // 清理临时文件
      try {
        await fs.rm(workDir, { recursive: true, force: true })
      } catch (error) {
        console.error('清理临时文件失败:', error)
      }
    }

  } catch (error) {
    console.error('硬字幕提取失败:', error)
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    })
  }
}

// 提取视频帧
async function extractVideoFrames(
  videoPath: string, 
  config: ExtractionConfig, 
  workDir: string
): Promise<Array<{ path: string; timestamp: number }>> {
  const frames: Array<{ path: string; timestamp: number }> = []
  const duration = config.endTime - config.startTime
  const frameCount = Math.floor(duration / config.frameInterval)

  for (let i = 0; i < frameCount; i++) {
    const timestamp = config.startTime + (i * config.frameInterval)
    const framePath = path.join(workDir, `frame_${i.toString().padStart(6, '0')}.jpg`)
    
    // 使用ffmpeg提取帧
    const command = `ffmpeg -ss ${timestamp} -i "${videoPath}" -vframes 1 -q:v 2 "${framePath}"`
    
    try {
      await execAsync(command, { timeout: 30000 })
      frames.push({ path: framePath, timestamp })
    } catch (error) {
      console.error(`提取帧 ${i} 失败:`, error)
    }
  }

  return frames
}

// 裁剪字幕区域
async function cropSubtitleRegion(
  imagePath: string,
  region: { x: number; y: number; width: number; height: number },
  workDir: string
): Promise<string> {
  const outputPath = path.join(workDir, `cropped_${path.basename(imagePath)}`)
  
  // 使用ffmpeg裁剪图像
  const command = `ffmpeg -i "${imagePath}" -filter:v "crop=${region.width}%:${region.height}%:${region.x}%:${region.y}%" "${outputPath}"`
  
  await execAsync(command, { timeout: 10000 })
  return outputPath
}

// 执行OCR识别
async function performOCR(imagePath: string): Promise<{ text: string; confidence: number }> {
  try {
    // 调用OCR API
    const response = await fetch('http://localhost:3000/api/ocr', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        imagePath: imagePath
      })
    })

    if (!response.ok) {
      throw new Error('OCR API调用失败')
    }

    const result = await response.json()
    
    if (result.success) {
      return {
        text: result.text || '',
        confidence: result.confidence || 0
      }
    } else {
      throw new Error(result.error || 'OCR识别失败')
    }
  } catch (error) {
    console.error('OCR识别失败:', error)
    return { text: '', confidence: 0 }
  }
}

// 计算文本相似度
function calculateSimilarity(text1: string, text2: string): number {
  if (!text1 || !text2) return 0
  
  const len1 = text1.length
  const len2 = text2.length
  const maxLen = Math.max(len1, len2)
  
  if (maxLen === 0) return 1
  
  // 简单的编辑距离算法
  const matrix: number[][] = []
  
  for (let i = 0; i <= len1; i++) {
    matrix[i] = [i]
  }
  
  for (let j = 0; j <= len2; j++) {
    matrix[0][j] = j
  }
  
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      if (text1[i - 1] === text2[j - 1]) {
        matrix[i][j] = matrix[i - 1][j - 1]
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + 1
        )
      }
    }
  }
  
  const distance = matrix[len1][len2]
  return 1 - (distance / maxLen)
}
