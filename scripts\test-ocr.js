// 测试OCR功能的脚本
const fs = require('fs')
const path = require('path')

async function testOCRSetup() {
  console.log('🔍 检查PP-OCRv5模型文件...')
  
  const modelDir = path.join(process.cwd(), 'models')
  const requiredFiles = ['det.onnx', 'rec.onnx', 'dict.txt']
  
  // 检查模型目录是否存在
  if (!fs.existsSync(modelDir)) {
    console.log('❌ models目录不存在，正在创建...')
    fs.mkdirSync(modelDir, { recursive: true })
  }
  
  // 检查必需的模型文件
  const missingFiles = []
  for (const file of requiredFiles) {
    const filePath = path.join(modelDir, file)
    if (!fs.existsSync(filePath)) {
      missingFiles.push(file)
    } else {
      const stats = fs.statSync(filePath)
      console.log(`✅ ${file} 存在 (${(stats.size / 1024 / 1024).toFixed(2)} MB)`)
    }
  }
  
  if (missingFiles.length > 0) {
    console.log('❌ 缺少以下模型文件:')
    missingFiles.forEach(file => console.log(`   - ${file}`))
    console.log('')
    console.log('请执行以下步骤:')
    console.log('1. 将D:\\.background\\modle文件夹下的模型文件复制到models目录')
    console.log('2. 确保文件名正确: det.onnx, rec.onnx, dict.txt')
    console.log('')
    console.log('Windows复制命令:')
    console.log('copy "D:\\.background\\modle\\*" "models\\"')
    return false
  }
  
  // 检查临时目录
  const tempDir = path.join(process.cwd(), 'temp')
  if (!fs.existsSync(tempDir)) {
    console.log('📁 创建临时目录...')
    fs.mkdirSync(tempDir, { recursive: true })
  }
  
  console.log('✅ 所有模型文件检查完成!')
  return true
}

async function testOCRAPI() {
  console.log('🧪 测试OCR API...')
  
  try {
    // 创建一个简单的测试图像（纯色图像）
    const testImageData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
    
    const response = await fetch('http://localhost:3000/api/ocr', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        imageData: testImageData
      })
    })
    
    if (response.ok) {
      const result = await response.json()
      console.log('✅ OCR API响应正常:', result)
      return true
    } else {
      console.log('❌ OCR API响应错误:', response.status, response.statusText)
      return false
    }
  } catch (error) {
    console.log('❌ OCR API测试失败:', error.message)
    return false
  }
}

async function main() {
  console.log('🚀 开始PP-OCRv5硬字幕提取功能测试...\n')
  
  const setupOk = await testOCRSetup()
  
  if (setupOk) {
    console.log('\n📡 测试API连接...')
    console.log('请确保开发服务器正在运行 (npm run dev)')
    console.log('如果服务器未运行，请先启动服务器再运行此测试\n')
    
    // 等待用户确认
    const readline = require('readline')
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    })
    
    rl.question('服务器是否正在运行? (y/n): ', async (answer) => {
      if (answer.toLowerCase() === 'y') {
        await testOCRAPI()
      } else {
        console.log('请先启动开发服务器: npm run dev')
      }
      rl.close()
    })
  } else {
    console.log('\n❌ 模型文件设置不完整，请先完成模型文件配置')
  }
}

if (require.main === module) {
  main().catch(console.error)
}

module.exports = { testOCRSetup, testOCRAPI }
